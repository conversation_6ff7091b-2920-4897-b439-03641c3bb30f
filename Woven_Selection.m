classdef Woven_Selection < handle
    properties
        last_factors = [];     % 记录每个个体上次使用的选择因素
        obj_flags = [];        % 添加属性保存非支配标志
        record;                % 添加record属性
    end
    
    methods
        function obj = Woven_Selection(record)
            obj.record = record;
        end 
        function selected = Stage1Selection(obj, old_dec, old_obj, new_dec, new_obj, new_con, index, PopDec, PopObj)
            dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
            if dom_result == 1
                selected = true;
            elseif dom_result == 0
                temp_PopObj = PopObj;
                temp_PopObj(index, :) = new_obj;

                % 计算当前种群的非支配解集合
                non_dominated_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);
                
                % 使用非支配解来计算被支配次数
                old_BeDomCT = Woven_Utility.calculateDominatedByNonDominatedSet(old_obj, PopObj, non_dominated_flags);
                
                % 为了计算新解的被支配次数，需要更新种群并重新计算非支配解
                temp_non_dominated_flags = Woven_Utility.calculateStage1NonDominatedSet(temp_PopObj);
                new_BeDomCT = Woven_Utility.calculateDominatedByNonDominatedSet(new_obj, temp_PopObj, temp_non_dominated_flags);
                
                if new_BeDomCT < old_BeDomCT
                    selected = true;
                elseif new_BeDomCT == old_BeDomCT % 被支配次数相同 则比较MED值
                    old_med = Woven_Utility.calculateMEDDistance(old_obj, index, PopObj);
                    new_med = Woven_Utility.calculateMEDDistance(new_obj, index, temp_PopObj);
                    selected = (new_med > old_med);
                else
                    selected = false;
                end
            else
                selected = false;
            end
        end
        function selected = Stage2Selection(obj, old_dec, old_obj, old_con, new_dec, new_obj, new_con, index, PopDec, PopObj, PopCon, epsilon_values, first_layer_flags)
                % 第二阶段选择函数 - 基于ε约束法的选择策略
                % 输入参数:
                %   old_dec, old_obj, old_con: 父代的决策变量、目标函数值、约束违反值
                %   new_dec, new_obj, new_con: 子代的决策变量、目标函数值、约束违反值
                %   index: 当前个体在种群中的索引
                %   PopDec, PopObj, PopCon: 整个种群的决策变量、目标函数值、约束违反值
                %   epsilon_values: 当前的ε约束值
                %   first_layer_flags: 第一前沿解的标记
                
                % 检查真实可行性
                parent_feasible = sum(max(0, old_con)) <= 0;
                child_feasible = sum(max(0, new_con)) <= 0;
                
                % 检查ε约束可行性
                parent_epsilon_feasible = Woven_Utility.checkEpsilonFeasibility(old_con, epsilon_values);
                child_epsilon_feasible = Woven_Utility.checkEpsilonFeasibility(new_con, epsilon_values);
                
                % 确定父代和子代的类型：1-可行解，2-收敛范围内解，3-完全不可行解
                if parent_feasible
                    parent_type = 1; % 可行解
                elseif parent_epsilon_feasible
                    parent_type = 2; % 收敛范围内解
                else
                    parent_type = 3; % 完全不可行解
                end
                
                if child_feasible
                    child_type = 1; % 可行解
                elseif child_epsilon_feasible
                    child_type = 2; % 收敛范围内解
                else
                    child_type = 3; % 完全不可行解
                end
                
                % 根据父代和子代类型进行选择，使用新的环境选择逻辑
                if parent_type == 1 && child_type == 1
                    % 情况1：可行解 → 可行解
                    selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, 'feasible_to_feasible');
                    
                elseif parent_type == 1 && child_type == 2
                    % 情况2：可行解 → 范围内约束解
                    selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, 'feasible_to_epsilon');
                    
                elseif parent_type == 1 && child_type == 3
                    % 情况3：可行解 → 不可行解
                    selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, 'feasible_to_infeasible');
                    
                elseif parent_type == 2 && child_type == 1
                    % 情况4：范围内约束解 → 可行解
                    selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, 'epsilon_to_feasible');
                    
                elseif parent_type == 2 && child_type == 2
                    % 情况5：范围内约束解 → 范围内约束解
                    selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, 'epsilon_to_epsilon');
                    
                elseif parent_type == 2 && child_type == 3
                    % 情况6：范围内约束解 → 不可行解
                    selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, 'epsilon_to_infeasible');
                    
                elseif parent_type == 3 && child_type == 1
                    % 情况7：约束不可行解 → 可行解
                    selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, 'infeasible_to_feasible');
                    
                elseif parent_type == 3 && child_type == 2
                    % 情况8：约束不可行解 → 范围内约束解
                    selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, 'infeasible_to_epsilon');
                    
                elseif parent_type == 3 && child_type == 3
                    % 情况9：约束不可行解 → 约束不可行解
                    selected = obj.Stage2EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, 'infeasible_to_infeasible');
                    
                else
                    selected = false;
                end
        end
        
        function selected = Stage3Selection(obj, old_dec, old_obj, old_con, new_dec, new_obj, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, Problem)
            % 第三阶段选择函数
            % 输入参数:
            %   old_dec, old_obj, old_con: 父代的决策变量、目标函数值、约束违反值
            %   new_dec, new_obj, new_con: 子代的决策变量、目标函数值、约束违反值
            %   idx: 当前个体在种群中的索引
            %   PopDec, PopObj, PopCon: 整个种群的决策变量、目标函数值、约束违反值
            %   first_layer_flags: 第一前沿解的标记
            %   Problem: 问题实例，用于获取当前评估次数
            % 返回: selected - true表示子代更好，false表示父代更好
            
            % 检查真实可行性（第三阶段中，可行解包括真实可行解和范围内约束解）
            % 但在当前实现中，第三阶段只处理真实可行性
            parent_feasible = sum(max(0, old_con)) <= 0;
            child_feasible = sum(max(0, new_con)) <= 0;
            
            % 根据父代和子代的可行性进行分类处理，使用新的环境选择逻辑
            if parent_feasible && child_feasible
                % 情况1：可行解 → 可行解（使用第一阶段策略）
                selected = obj.Stage3EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, 'feasible_to_feasible', Problem);
                
            elseif parent_feasible && ~child_feasible
                % 情况2：可行解 → 不可行解
                selected = obj.Stage3EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, 'feasible_to_infeasible', Problem);
                
            elseif ~parent_feasible && child_feasible
                % 情况3：不可行解 → 可行解（使用第二阶段范围内约束解策略）
                selected = obj.Stage3EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, 'infeasible_to_feasible', Problem);
                
            else
                % 情况4：不可行解 → 不可行解（使用第二阶段范围内约束解策略）
                selected = obj.Stage3EnvironmentSelection(old_obj, new_obj, old_dec, new_dec, old_con, new_con, idx, PopDec, PopObj, PopCon, first_layer_flags, 'infeasible_to_infeasible', Problem);
            end
        end

        function child_better = Stage2SelectionScore(obj, parent_dec, parent_obj, child_dec, child_obj, index, PopDec, PopObj)
            % Stage2中的综合评分比较（基于加权求和）
            % parent_dec, parent_obj: 父代的决策变量和目标函数值
            % child_dec, child_obj: 子代的决策变量和目标函数值
            % index: 解在种群中的索引
            % PopDec, PopObj: 整个种群的决策变量和目标函数值
            % 返回: child_better - true表示子代更好，false表示父代更好
            
            % 获取权重
            weights = obj.record.stage2_factor_weights;
            
            % 1. 计算角度评分并比较
            parent_angle_score = Woven_Utility.Stage2AngleScore(parent_obj, index, PopObj);
            child_angle_score = Woven_Utility.Stage2AngleScore(child_obj, index, PopObj);
            angle_factor = double(child_angle_score > parent_angle_score);
            
            % 2. 计算MED距离并比较
            parent_med_score = Woven_Utility.calculateMEDDistance(parent_obj, index, PopObj);
            child_med_score = Woven_Utility.calculateMEDDistance(child_obj, index, PopObj);
            med_factor = double(child_med_score > parent_med_score);
            
            % 3. 计算决策空间多样性并比较
            parent_diversity_score = Woven_Utility.Stage2DecisionSpaceDiversity(parent_dec, index, PopDec);
            child_diversity_score = Woven_Utility.Stage2DecisionSpaceDiversity(child_dec, index, PopDec);
            diversity_factor = double(child_diversity_score > parent_diversity_score);
            
            % 4. 计算加权总分
            parent_total_score = 0; % 父代作为基准，总分为0
            child_total_score = angle_factor * weights.angle + ...
                               med_factor * weights.med + ...
                               diversity_factor * weights.decision_space_diversity;
            
            % 5. 根据总分比较决定选择
            child_better = (child_total_score > parent_total_score);
            
            % 可选：输出选择结果用于调试（取消注释以启用）
            % fprintf('Stage2选择: 父代总分=%.4f, 子代总分=%.4f, 选择=%s\n', ...
            %     parent_total_score, child_total_score, ...
            %     char(ternary(child_better, '子代', '父代')));
        end

        function child_better = Stage3SelectionScore(obj, parent_dec, parent_obj, child_dec, child_obj, index, PopDec, PopObj)
            % Stage3中的综合评分比较（基于加权求和）
            % parent_dec, parent_obj: 父代的决策变量和目标函数值
            % child_dec, child_obj: 子代的决策变量和目标函数值
            % index: 解在种群中的索引
            % PopDec, PopObj: 整个种群的决策变量和目标函数值
            % 返回: child_better - true表示子代更好，false表示父代更好
            
            % 获取权重
            weights = obj.record.stage3_factor_weights;
            
            % 1. 计算角度评分并比较
            parent_angle_score = Woven_Utility.Stage2AngleScore(parent_obj, index, PopObj);
            child_angle_score = Woven_Utility.Stage2AngleScore(child_obj, index, PopObj);
            angle_factor = double(child_angle_score > parent_angle_score);
            
            % 2. 计算MED距离并比较
            parent_med_score = Woven_Utility.calculateMEDDistance(parent_obj, index, PopObj);
            child_med_score = Woven_Utility.calculateMEDDistance(child_obj, index, PopObj);
            med_factor = double(child_med_score > parent_med_score);
            
            % 3. 计算拥挤距离并比较
            parent_crowding_score = Woven_Utility.calculateCrowdingDistance(parent_obj, index, PopObj);
            temp_obj = PopObj(index,:);
            PopObj(index,:) = child_obj;
            child_crowding_score = Woven_Utility.calculateCrowdingDistance(child_obj, index, PopObj);
            PopObj(index,:) = temp_obj;
            diversity_factor = double(child_crowding_score > parent_crowding_score);
            
            % 4. 计算加权总分
            parent_total_score = 0; % 父代作为基准，总分为0
            child_total_score = angle_factor * weights.angle + ...
                               med_factor * weights.med + ...
                               diversity_factor * weights.diversity;
            
            % 5. 根据总分比较决定选择
            child_better = (child_total_score > parent_total_score);
            
            % 可选：输出选择结果用于调试（取消注释以启用）
            % fprintf('Stage3选择: 父代总分=%.4f, 子代总分=%.4f, 选择=%s\n', ...
            %     parent_total_score, child_total_score, ...
            %     char(ternary(child_better, '子代', '父代')));
        end
        function selected = Stage2EnvironmentSelection(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, epsilon_values, selection_case)
            % 第二阶段环境选择函数 - 实现完整的9种情况选择逻辑
            % 输入参数:
            %   old_obj, new_obj: 父代和子代的目标函数值
            %   old_dec, new_dec: 父代和子代的决策变量
            %   old_con, new_con: 父代和子代的约束违反值
            %   index: 当前个体在种群中的索引
            %   PopDec, PopObj, PopCon: 整个种群的决策变量、目标函数值、约束违反值
            %   first_layer_flags: 第一前沿解的标记（只包括真实可行解）
            %   epsilon_values: 当前的ε约束值
            %   selection_case: 选择情况标识字符串
            % 返回: selected - true表示选择子代，false表示选择父代
            
            switch selection_case
                case 'feasible_to_feasible'
                    % 情况1：可行解 → 可行解
                    % 1. 新解支配旧解 → 接受新解
                    % 2. 互不支配 → 检查多样性是否比旧解好
                    % 3. 多样性没有旧解好或相等 → 拒绝新解
                    
                    dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
                    if dom_result == 1
                        % 新解支配旧解
                        selected = true;
                    elseif dom_result == 0
                        % 互不支配，比较多样性
                        selected = obj.Stage2SelectionScore(old_dec, old_obj, new_dec, new_obj, index, PopDec, PopObj);
                    else
                        % 旧解支配新解
                        selected = false;
                    end
                    
                case 'feasible_to_epsilon'
                    % 情况2：可行解 → 范围内约束解
                    % 新解支配旧解且多样性比旧解好 → 接受新解，否则拒绝
                    
                    dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
                    if dom_result == 1
                        % 新解支配旧解，还需要检查多样性
                        diversity_better = obj.Stage2SelectionScore(old_dec, old_obj, new_dec, new_obj, index, PopDec, PopObj);
                        selected = diversity_better;
                    else
                        % 不支配或被支配，拒绝
                        selected = false;
                    end
                    
                case 'feasible_to_infeasible'
                    % 情况3：可行解 → 不可行解
                    % 直接拒绝
                    selected = false;
                    
                case 'epsilon_to_feasible'
                    % 情况4：范围内约束解 → 可行解
                    % 1. 新解支配旧解 → 接受新解
                    % 2. 互不支配 → 比较被第一前沿支配次数
                    % 3. 被支配次数更少 → 接受新解
                    % 4. 被支配次数相等 → 检查多样性是否更好
                    % 5. 多样性更好 → 接受新解，否则拒绝
                    
                    dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
                    if dom_result == 1
                        % 新解支配旧解
                        selected = true;
                    elseif dom_result == 0
                        % 互不支配，比较被第一前沿支配次数
                        old_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(old_obj, PopObj, first_layer_flags);
                        new_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(new_obj, PopObj, first_layer_flags);
                        
                        if new_dominated_count < old_dominated_count
                            % 被支配次数更少
                            selected = true;
                        elseif new_dominated_count == old_dominated_count
                            % 被支配次数相等，比较多样性
                            selected = obj.Stage2SelectionScore(old_dec, old_obj, new_dec, new_obj, index, PopDec, PopObj);
                        else
                            % 被支配次数更多
                            selected = false;
                        end
                    else
                        % 旧解支配新解
                        selected = false;
                    end
                    
                case 'epsilon_to_epsilon'
                    % 情况5：范围内约束解 → 范围内约束解
                    % 逻辑与情况4相同
                    
                    dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
                    if dom_result == 1
                        % 新解支配旧解
                        selected = true;
                    elseif dom_result == 0
                        % 互不支配，比较被第一前沿支配次数
                        old_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(old_obj, PopObj, first_layer_flags);
                        new_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(new_obj, PopObj, first_layer_flags);
                        
                        if new_dominated_count < old_dominated_count
                            % 被支配次数更少
                            selected = true;
                        elseif new_dominated_count == old_dominated_count
                            % 被支配次数相等，比较多样性
                            selected = obj.Stage2SelectionScore(old_dec, old_obj, new_dec, new_obj, index, PopDec, PopObj);
                        else
                            % 被支配次数更多
                            selected = false;
                        end
                    else
                        % 旧解支配新解
                        selected = false;
                    end
                    
                case 'epsilon_to_infeasible'
                    % 情况6：范围内约束解 → 不可行解
                    % 直接拒绝
                    selected = false;
                    
                case 'infeasible_to_feasible'
                    % 情况7：约束不可行解 → 可行解
                    % 直接接受
                    selected = true;
                    
                case 'infeasible_to_epsilon'
                    % 情况8：约束不可行解 → 范围内约束解
                    % 直接接受
                    selected = true;
                    
                case 'infeasible_to_infeasible'
                    % 情况9：约束不可行解 → 约束不可行解
                    % 新解违反约束程度更小 → 接受新解，否则保持旧解
                    
                    old_cv = sum(max(0, old_con));
                    new_cv = sum(max(0, new_con));
                    selected = (new_cv < old_cv);
                    
                otherwise
                    error('Stage2EnvironmentSelection: 未知的选择情况 - %s', selection_case);
            end
        end
        
        function selected = Stage3EnvironmentSelection(obj, old_obj, new_obj, old_dec, new_dec, old_con, new_con, index, PopDec, PopObj, PopCon, first_layer_flags, selection_case, Problem)
            % 第三阶段环境选择函数 - 基于新策略的4种情况选择逻辑
            %
            % 选择策略：
            % 1. 可行解→可行解:
            %    - 新解支配旧解 → 接受新解
            %    - 互不支配 → 检查多样性是否比旧解好
            %    - 多样性没有旧解好或相等 → 拒绝新解
            % 2. 可行解→不可行解: 直接拒绝
            % 3. 不可行解→可行解:
            %    - 多样性更好，则接受新解
            %    - 否则拒绝
            % 4. 不可行解→不可行解:
            %    - 前50%：如果在第一前沿支配次数相同或更少的情况下CV更小则接受，否则拒绝
            %    - 后50%：直接强制收敛CV，确保结果好一点
            %
            % 输入参数:
            %   old_obj, new_obj: 父代和子代的目标函数值
            %   old_dec, new_dec: 父代和子代的决策变量
            %   old_con, new_con: 父代和子代的约束违反值
            %   index: 当前个体在种群中的索引
            %   PopDec, PopObj, PopCon: 整个种群的决策变量、目标函数值、约束违反值
            %   first_layer_flags: 第一前沿解的标记（只包括真实可行解）
            %   selection_case: 选择情况标识字符串
            %   Problem: 问题实例，用于获取当前评估次数
            % 返回: selected - true表示选择子代，false表示选择父代
            
            switch selection_case
                case 'feasible_to_feasible'
                    % 情况1：可行解 → 可行解
                    % 1. 新解支配旧解 → 接受新解
                    % 2. 互不支配 → 检查多样性是否比旧解好
                    % 3. 多样性没有旧解好或相等 → 拒绝新解
                    
                    dom_result = Woven_Utility.checkDominance(new_obj, old_obj);
                    if dom_result == 1
                        % 新解支配旧解 → 接受新解
                        selected = true;
                    elseif dom_result == 0
                        % 互不支配 → 检查多样性是否比旧解好
                        selected = obj.Stage3SelectionScore(old_dec, old_obj, new_dec, new_obj, index, PopDec, PopObj);
                    else
                        % 旧解支配新解 → 拒绝新解
                        selected = false;
                    end
                    
                case 'feasible_to_infeasible'
                    % 情况2：可行解 → 不可行解
                    % 拒绝新解
                    selected = false;
                    
                case 'infeasible_to_feasible'
                    % 情况3：不可行解 → 可行解
                    % 直接接受
                    selected = true;
                    
                case 'infeasible_to_infeasible'
                    % 情况4：不可行解 → 不可行解
                    % 计算第三阶段已使用的评估次数和总可用评估次数
                    stage3_elapsed_fe = Problem.FE - obj.record.stage3_start_fe;
                    stage3_total_fe = Problem.maxFE - obj.record.stage3_start_fe;
                    
                    % 计算第三阶段进度百分比
                    stage3_progress = stage3_elapsed_fe / stage3_total_fe;
                    
                    if stage3_progress <= 0.5
                        % 前50%：如果在第一前沿支配次数相同或更少的情况下CV更小则接受，否则拒绝
                        old_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(old_obj, PopObj, first_layer_flags);
                        new_dominated_count = Woven_Utility.calculateDominatedByFirstLayerCount(new_obj, PopObj, first_layer_flags);
                        
                        if new_dominated_count <= old_dominated_count
                            % 被支配次数相同或更少，比较CV（约束违反值）
                            old_cv = sum(max(0, old_con));
                            new_cv = sum(max(0, new_con));
                            
                            % CV更小则接受新解
                            selected = (new_cv < old_cv);
                        else
                            % 被支配次数更多 → 拒绝新解
                            selected = false;
                        end
                    else
                        % 后50%：直接强制收敛CV，确保结果好一点
                        old_cv = sum(max(0, old_con));
                        new_cv = sum(max(0, new_con));
                        
                        % 只要CV更小就接受，强制收敛
                        selected = (new_cv < old_cv);
                    end
                    
                otherwise
                    error('Stage3EnvironmentSelection: 未知的选择情况 - %s', selection_case);
            end
        end
    end
end