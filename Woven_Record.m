classdef Woven_Record < handle    
    properties
        % SHADE基础参数
        stage2_start_fe;      % 阶段2开始评估次数
        stage3_start_fe;      % 阶段3开始评估次数
        
        % 变异阶段相关参数
        stage1_normal_prob = 0.5;    % 阶段1正态变异概率
        stage2_normal_prob = 0.99;   % 阶段2正态变异概率
        stage3_normal_prob = 0.99;   % 阶段3正态变异概率
        
        % 扰动相关参数
        stage1_sigma = 0.8;    % 阶段1高斯扰动幅度比例
        stage2_sigma = 0.1;          % 阶段2高斯扰动幅度
        stage3_sigma = 0.1;          % 阶段3高斯扰动幅度
        
        % 多样性相关参数
        diversity_threshold = 0.3;   % 多样性阈值
        disturbance_ratio = 0.1;     % 扰动比例(维度百分比)
        disturbance_scale = 0.2;     % 扰动幅度
        
        % 随机扰动相关
        random_disturbance_prob = 0.3; % 随机扰动概率
        random_disturbance_ratio = 0.1; % 随机扰动比例(维度百分比)
        random_disturbance_scale = 0.2; % 随机扰动幅度
        
        % 大规模扰动参数
        large_disturbance_ratio = 0.3; % 大规模扰动比例(维度百分比)
        large_disturbance_scale = 0.4; % 大规模扰动幅度
        
        % StageControl相关参数
        stage1_min_fe = 0.25;          % 阶段1最小评估比例
        stage1_max_fe = 0.45;           % 阶段1最大评估比例
        stage2_max_fe = 0.75;          % 阶段2最大评估比例
        non_dominated_threshold = 0.93;  % 一二级非支配解阈值
        first_front_ratio = 0.60;      % 第一层非支配解阈值
        interwoven_threshold = 0.6;     % 边界交织阈值   要求的可行解比例是多少
        consecutive_gen_threshold = 3;  % 连续N代无显著变化的阈值
        

        angle_division = 10;            % 角度划分数量
        
        % 选择因子权重 - 阶段2
        stage2_factor_weights = struct(...
            'decision_space_diversity', 0.10, ...
            'angle', 0.30, ...
            'med', 0.60 ...
        );
        
        % 选择因子权重 - 阶段3
        stage3_factor_weights = struct(...
            'angle', 0.30, ...
            'med', 0.60, ...
            'diversity', 0.10 ...
        );

        tau_initial = 0.6;         % 初始容忍因子
        constraint_beta = 0.3;     % 约束收缩指数
    end
    
    methods
        function obj = initializeParameters(obj, memory_size, archive_rate, population_size, angle_division)
            
            % 重置其他参数
            obj.memory_pos = 1;
            obj.success_cr = [];
            obj.success_f = [];
            obj.success_weight = [];
            obj.current_cr = 0.5;
            obj.current_f = 0.5;
        end
    end
    
    methods (Static)
        function obj = getInstance(varargin)
            persistent localInstance   % 使用persistent变量实现单例
            if isempty(localInstance) || ~isvalid(localInstance)
                localInstance = Woven_Record();
                % 如果提供了参数则进行初始化
                if ~isempty(varargin)
                    localInstance.initializeParameters(varargin{:});
                end

            elseif ~isempty(varargin)
                localInstance.initializeParameters(varargin{:});
            end
            obj = localInstance;
        end
    end
    
    methods (Access = private)
        function obj = Woven_Record()
        end
    end
end
