classdef Woven_Mutation < handle
    properties
        record;  % Woven_Record 对象，用于管理参数
    end

    methods
        % 构造函数，初始化记录对象
        function obj = Woven_Mutation(record)
            obj.record = record;
        end

        function offspring = mutate(obj, candidate, stage, fitness, Problem, PopDec, PopObj, PopCon, current_index)
            % 新的变异函数接口
            % candidate: 当前个体的决策变量
            % stage: 当前阶段 (1, 2, 3)
            % fitness: 当前个体的适应度值（目标函数值）
            % Problem: 问题对象
            % PopDec: 完整种群的决策变量矩阵
            % PopObj: 完整种群的目标函数值矩阵
            % PopCon: 完整种群的约束违反值矩阵
            % current_index: 当前个体在种群中的索引

            [~, D] = size(candidate);
            offspring = candidate;

            if stage == 1
                % 阶段1: 结合GA和DE的混合策略
                if rand < obj.record.stage1_normal_prob
                    % 使用GA高斯变异
                    k = randi(D);
                    distance_to_boundary = min(candidate(k), 1 - candidate(k));
                    sigma = min(0.5, distance_to_boundary * obj.record.stage1_sigma);
                    temp = candidate(k) + randn * sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end
                else
                    % 多版本DE算法测试
                    % 临时变量控制使用哪个DE算法版本 (1-6)
                    DE_VERSION = 2;

                    % 使用完整种群进行DE变异
                    offspring = obj.performDEMutation(candidate, PopDec, current_index, DE_VERSION, D);
                end
            elseif stage == 2
                % 阶段2: 使用Record中的概率参数
                if rand < obj.record.stage2_normal_prob
                    % 正态变异
                    k = randi(D);
                    temp = candidate(k) + randn * obj.record.stage2_sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end
                else
                    % DE/rand/1变异，使用完整种群
                    offspring = obj.performStage2DEMutation(candidate, PopDec, current_index, D);
                end

            elseif stage == 3
                % 阶段3: 使用Record中的概率参数
                if rand < obj.record.stage3_normal_prob
                    % 正态变异
                    k = randi(D);
                    temp = candidate(k) + randn * obj.record.stage3_sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end
                else
                    % DE/rand/2变异，使用完整种群
                    offspring = obj.performStage3DEMutation(candidate, PopDec, current_index, D);
                end
            end
        end

        % ========== DE变异实现函数 ==========

        function offspring = performDEMutation(obj, candidate, PopDec, current_index, DE_VERSION, D)
            % 第一阶段DE变异的统一入口
            [N, ~] = size(PopDec);

            switch DE_VERSION
                case 1
                    % 版本1: 经典DE/rand/1
                    offspring = obj.DE_Version1_Rand1(candidate, PopDec, current_index, D);

                case 2
                    % 版本2: DE/best/1
                    offspring = obj.DE_Version2_Best1(candidate, PopDec, current_index, D);

                case 3
                    % 版本3: DE/current-to-best/1
                    offspring = obj.DE_Version3_CurrentToBest1(candidate, PopDec, current_index, D);

                case 4
                    % 版本4: DE/rand/2
                    offspring = obj.DE_Version4_Rand2(candidate, PopDec, current_index, D);

                case 5
                    % 版本5: 自适应DE (根据种群大小选择策略)
                    offspring = obj.DE_Version5_Adaptive(candidate, PopDec, current_index, D);

                case 6
                    % 版本6: 混合DE (多策略随机选择)
                    offspring = obj.DE_Version6_Hybrid(candidate, PopDec, current_index, D);

                otherwise
                    % 默认使用DE/rand/1
                    offspring = obj.DE_Version1_Rand1(candidate, PopDec, current_index, D);
            end
        end

        function offspring = performStage2DEMutation(obj, candidate, PopDec, current_index, D)
            % 第二阶段DE/rand/1变异
            [N, ~] = size(PopDec);
            if N >= 3
                % 排除当前个体，从其他个体中选择3个
                other_indices = setdiff(1:N, current_index);
                if length(other_indices) >= 3
                    selected = other_indices(randperm(length(other_indices), 3));
                    r1 = PopDec(selected(1), :);
                    r2 = PopDec(selected(2), :);
                    r3 = PopDec(selected(3), :);

                    F = rand;
                    v = r1 + F * (r2 - r3);
                    offspring = max(0, min(1, v));
                else
                    offspring = obj.DE_Fallback_Polynomial(candidate, D);
                end
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = performStage3DEMutation(obj, candidate, PopDec, current_index, D)
            % 第三阶段DE/rand/2变异
            [N, ~] = size(PopDec);
            if N >= 5
                % 排除当前个体，从其他个体中选择5个
                other_indices = setdiff(1:N, current_index);
                if length(other_indices) >= 5
                    selected = other_indices(randperm(length(other_indices), 5));
                    r1 = PopDec(selected(1), :);
                    r2 = PopDec(selected(2), :);
                    r3 = PopDec(selected(3), :);
                    r4 = PopDec(selected(4), :);
                    r5 = PopDec(selected(5), :);

                    F = 0.4 + 0.6 * rand; % F在[0.4, 1.0]范围内
                    v = r1 + F * (r2 - r3) + F * (r4 - r5);
                    offspring = max(0, min(1, v));
                else
                    % 退化为DE/rand/1
                    offspring = obj.performStage2DEMutation(candidate, PopDec, current_index, D);
                end
            elseif N >= 3
                % 退化为DE/rand/1
                offspring = obj.performStage2DEMutation(candidate, PopDec, current_index, D);
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        % ========== DE算法版本实现 ==========

        function offspring = DE_Version1_Rand1(obj, candidate, PopDec, current_index, D)
            % 版本1: 经典DE/rand/1 - v = r1 + F * (r2 - r3)
            [N, ~] = size(PopDec);
            if N >= 3
                % 排除当前个体，从其他个体中选择3个
                other_indices = setdiff(1:N, current_index);
                if length(other_indices) >= 3
                    selected = other_indices(randperm(length(other_indices), 3));
                    r1 = PopDec(selected(1), :);
                    r2 = PopDec(selected(2), :);
                    r3 = PopDec(selected(3), :);

                    F = 0.4 + 0.6 * rand; % F在[0.4, 1.0]范围内
                    v = r1 + F * (r2 - r3);
                    offspring = max(0, min(1, v));
                else
                    offspring = obj.DE_Fallback_Polynomial(candidate, D);
                end
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version2_Best1(obj, candidate, PopDec, current_index, D)
            % 版本2: DE/best/1 - v = best + F * (r1 - r2)
            [N, ~] = size(PopDec);
            if N >= 3
                % 排除当前个体
                other_indices = setdiff(1:N, current_index);
                if length(other_indices) >= 3
                    % 简化：随机选择一个作为"最优"个体
                    best_idx = other_indices(randi(length(other_indices)));
                    best = PopDec(best_idx, :);

                    % 从剩余个体中选择两个
                    remaining_indices = setdiff(other_indices, best_idx);
                    if length(remaining_indices) >= 2
                        selected = remaining_indices(randperm(length(remaining_indices), 2));
                        r1 = PopDec(selected(1), :);
                        r2 = PopDec(selected(2), :);
                    else
                        % 不足时从所有其他个体中选择
                        selected = other_indices(randperm(length(other_indices), 2));
                        r1 = PopDec(selected(1), :);
                        r2 = PopDec(selected(2), :);
                    end

                    F = 0.4 + 0.6 * rand;
                    v = best + F * (r1 - r2);
                    offspring = max(0, min(1, v));
                else
                    offspring = obj.DE_Fallback_Polynomial(candidate, D);
                end
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version3_CurrentToBest1(obj, candidate, PopDec, current_index, D)
            % 版本3: DE/current-to-best/1 - v = candidate + F * (best - candidate) + F * (r1 - r2)
            [N, ~] = size(PopDec);
            if N >= 3
                % 排除当前个体
                other_indices = setdiff(1:N, current_index);
                if length(other_indices) >= 3
                    % 选择"最优"个体
                    best_idx = other_indices(randi(length(other_indices)));
                    best = PopDec(best_idx, :);

                    % 从剩余个体中选择两个
                    remaining_indices = setdiff(other_indices, best_idx);
                    if length(remaining_indices) >= 2
                        selected = remaining_indices(randperm(length(remaining_indices), 2));
                        r1 = PopDec(selected(1), :);
                        r2 = PopDec(selected(2), :);
                    else
                        % 不足时从所有其他个体中选择
                        selected = other_indices(randperm(length(other_indices), 2));
                        r1 = PopDec(selected(1), :);
                        r2 = PopDec(selected(2), :);
                    end

                    F = 0.4 + 0.6 * rand;
                    v = candidate + F * (best - candidate) + F * (r1 - r2);
                    offspring = max(0, min(1, v));
                else
                    offspring = obj.DE_Fallback_Polynomial(candidate, D);
                end
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version4_Rand2(obj, candidate, PopDec, current_index, D)
            % 版本4: DE/rand/2 - v = r1 + F * (r2 - r3) + F * (r4 - r5)
            [N, ~] = size(PopDec);
            if N >= 5
                % 排除当前个体，从其他个体中选择5个
                other_indices = setdiff(1:N, current_index);
                if length(other_indices) >= 5
                    selected = other_indices(randperm(length(other_indices), 5));
                    r1 = PopDec(selected(1), :);
                    r2 = PopDec(selected(2), :);
                    r3 = PopDec(selected(3), :);
                    r4 = PopDec(selected(4), :);
                    r5 = PopDec(selected(5), :);

                    F = 0.4 + 0.6 * rand;
                    v = r1 + F * (r2 - r3) + F * (r4 - r5);
                    offspring = max(0, min(1, v));
                else
                    % 退化为DE/rand/1
                    offspring = obj.DE_Version1_Rand1(candidate, PopDec, current_index, D);
                end
            elseif N >= 3
                % 退化为DE/rand/1
                offspring = obj.DE_Version1_Rand1(candidate, PopDec, current_index, D);
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version5_Adaptive(obj, candidate, PopDec, current_index, D)
            % 版本5: 自适应DE - 根据种群大小选择不同策略
            [N, ~] = size(PopDec);
            if N >= 10
                % 种群充足时使用DE/rand/2
                offspring = obj.DE_Version4_Rand2(candidate, PopDec, current_index, D);
            elseif N >= 5
                % 中等种群时使用DE/current-to-best/1
                offspring = obj.DE_Version3_CurrentToBest1(candidate, PopDec, current_index, D);
            elseif N >= 3
                % 种群较少时使用DE/rand/1
                offspring = obj.DE_Version1_Rand1(candidate, PopDec, current_index, D);
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version6_Hybrid(obj, candidate, PopDec, current_index, D)
            % 版本6: 混合DE - 随机选择不同策略
            [N, ~] = size(PopDec);
            if N >= 3
                strategy = randi(3); % 随机选择1-3种策略
                switch strategy
                    case 1
                        offspring = obj.DE_Version1_Rand1(candidate, PopDec, current_index, D);
                    case 2
                        offspring = obj.DE_Version2_Best1(candidate, PopDec, current_index, D);
                    case 3
                        offspring = obj.DE_Version3_CurrentToBest1(candidate, PopDec, current_index, D);
                end
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Fallback_Polynomial(obj, candidate, D)
            % 种群不足时的后备多项式变异
            offspring = candidate;
            eta_m = 20;
            for j = 1:D
                if rand <= 1.0/D
                    u = rand;
                    if u <= 0.5
                        delta = (2*u)^(1/(eta_m+1)) - 1;
                    else
                        delta = 1 - (2*(1-u))^(1/(eta_m+1));
                    end
                    offspring(j) = candidate(j) + delta * obj.record.stage1_sigma;
                    offspring(j) = max(0, min(1, offspring(j)));
                end
            end
        end
    end
end