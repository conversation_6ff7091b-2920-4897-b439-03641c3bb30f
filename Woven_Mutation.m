classdef Woven_Mutation < handle    
    properties
        record;  % Woven_Record 对象，用于管理参数和存档
    end
    
    methods
        % 构造函数，初始化记录对象
        function obj = Woven_Mutation(record)
            obj.record = record;
        end
        
        function obj = initializeParameters(obj, memory_size, archive_rate, population_size)
            % 调用记录对象的初始化方法
            obj.record.initializeParameters(memory_size, archive_rate, population_size);
        end
        
        function offspring = mutate(obj, candidate, stage, fitness, Problem) 
            [~, D] = size(candidate);
            offspring = candidate;
            if stage == 1
                % 阶段1: 结合GA和DE/transfer/1的混合策略
                if rand < obj.record.stage1_normal_prob
                    % 使用GA高斯变异
                    k = randi(D);
                    distance_to_boundary = min(candidate(k), 1 - candidate(k));
                    sigma = min(0.5, distance_to_boundary * obj.record.stage1_sigma_scale);
                    temp = candidate(k) + randn * sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end
                else
                    % 多版本DE算法测试
                    % 临时变量控制使用哪个DE算法版本 (1-6)
                    DE_VERSION = 6; % 修改这个数字来测试不同版本

                    population_size = size(obj.record.archive, 1);

                    switch DE_VERSION
                        case 1
                            % 版本1: 经典DE/rand/1
                            offspring = obj.DE_Version1_Rand1(candidate, population_size, D);

                        case 2
                            % 版本2: DE/best/1
                            offspring = obj.DE_Version2_Best1(candidate, population_size, D);

                        case 3
                            % 版本3: DE/current-to-best/1
                            offspring = obj.DE_Version3_CurrentToBest1(candidate, population_size, D);

                        case 4
                            % 版本4: DE/rand/2
                            offspring = obj.DE_Version4_Rand2(candidate, population_size, D);

                        case 5
                            % 版本5: 自适应DE (根据存档大小选择策略)
                            offspring = obj.DE_Version5_Adaptive(candidate, population_size, D);

                        case 6
                            % 版本6: 混合DE (多策略随机选择)
                            offspring = obj.DE_Version6_Hybrid(candidate, population_size, D);

                        otherwise
                            % 默认使用原始DE/transfer/1
                            offspring = obj.DE_Original_Transfer1(candidate, population_size, D);
                    end
                end
            elseif stage == 2
                % 阶段2: 使用Record中的概率参数
                if rand < obj.record.stage2_normal_prob
                    % 正态变异
                    k = randi(D);
                    temp = candidate(k) + randn * obj.record.stage2_sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end                
                else
                    % DE/rand/1变异
                    population_size = size(obj.record.archive, 1);
                    if population_size >= 3
                        indices = randperm(population_size, 3);
                        r1 = obj.record.archive(indices(1), :);
                        r2 = obj.record.archive(indices(2), :);
                        r3 = obj.record.archive(indices(3), :);
                        
                        F = rand;
                        v = r1 + F * (r2 - r3);
                        
                        % 边界处理
                        v = max(0, min(1, v));
                        % 更新子代 - 不使用逻辑索引，而是直接赋值
                        offspring = v;
                    end
                end
            elseif stage == 3
                % 阶段3: 使用Record中的概率参数
                if rand < obj.record.stage3_normal_prob
                    % 正态变异
                    k = randi(D);
                    temp = candidate(k) + randn * obj.record.stage3_sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end                
                else
                    % 随机性更高的DE变异策略 - DE/rand/2
                    population_size = size(obj.record.archive, 1);
                    if population_size >= 5  % DE/rand/2需要至少5个不同的个体
                        % 随机选择5个不同的个体
                        indices = randperm(population_size, 5);
                        r1 = obj.record.archive(indices(1), :);
                        r2 = obj.record.archive(indices(2), :);
                        r3 = obj.record.archive(indices(3), :);
                        r4 = obj.record.archive(indices(4), :);
                        r5 = obj.record.archive(indices(5), :);
                        
                        % 控制参数F，使用Record中的范围参数
                        F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                        
                        % DE/rand/2变异操作
                        v = r1 + F * (r2 - r3) + F * (r4 - r5);
                
                        % 边界处理
                        v = max(0, min(1, v));
                        % 直接赋值
                        offspring = v;
                    elseif population_size >= 3  % 如果个体不足5个，退化为DE/rand/1
                        % 随机选择3个不同的个体
                        indices = randperm(population_size, 3);
                        r1 = obj.record.archive(indices(1), :);
                        r2 = obj.record.archive(indices(2), :);
                        r3 = obj.record.archive(indices(3), :);
            
                        % 控制参数F，使用Record中的范围参数
                        F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
            
                        % DE/rand/1变异操作
                        v = r1 + F * (r2 - r3);
                
                        % 边界处理
                        v = max(0, min(1, v));
                        % 直接赋值
                        offspring = v;
                    elseif population_size >= 1  % 如果个体非常少，使用当前个体加随机扰动
                        % 随机选择一个存档个体
                        idx = randi(population_size);
                        r1 = obj.record.archive(idx, :);
            
                        % 大幅度随机扰动，使用Record中的参数
                        v = r1;
                        num_dims = max(1, round(D * obj.record.large_disturbance_ratio));
                        dims = randperm(D, num_dims);
                        for dim = dims
                            v(dim) = v(dim) + (rand - 0.5) * obj.record.large_disturbance_scale;
                        end
            
                        % 边界处理
                        v = max(0, min(1, v));
                        % 直接赋值
                        offspring = v;
                    end
                end
            elseif stage == 4
                if rand < obj.record.stage1_normal_prob
                    % 使用正态变异
                    k = randi(D);
                    distance_to_boundary = min(candidate(k), 1 - candidate(k));
                    sigma = min(0.5, distance_to_boundary * obj.record.stage1_sigma_scale);
                    temp = candidate(k) + randn * sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end
                else
                    % 使用SHADE生成后代
                    offspring = obj.generateOffspringSHADE(candidate, fitness, Problem);
                end  
            end
        end
        
        function offspring = generateOffspringSHADE(obj, candidate, fitness, Problem)
            % 生成子代个体
            % candidate: 当前种群
            % fitness: 种群的适应度值/目标函数值(越小越好)
            % Problem: 优化问题对象，用于获取当前评估次数和最大评估次数
            
            [~, D] = size(candidate);
            
            % 从记忆库中随机选择索引
            r_idx = randi(obj.record.memory_size);
            
            % 生成交叉率CR - 使用截断正态分布
            mu_cr = obj.record.memory_cr(r_idx);
            if mu_cr < 0.001
                cr = 0;
            else
                cr = normrnd(mu_cr, 0.1);
                cr = max(0, min(1, cr));
            end
            
            % 生成缩放因子F - 使用柯西分布
            mu_f = obj.record.memory_f(r_idx);
            if mu_f < 0.001
                f = 0.1;  % 确保F不会太小
            else
                f = mu_f + 0.1 * tan(pi * (rand - 0.5));
                while f <= 0 || f > 1  % 确保F在(0,1]范围内
                    f = mu_f + 0.1 * tan(pi * (rand - 0.5));
                end
                f = min(1.0, f);
            end
            
            % 记录当前使用的参数
            obj.record.current_cr = cr;
            obj.record.current_f = f;
            
            % 从当前种群和归档中选择个体
            if isempty(obj.record.archive)
                population = candidate;
            else
                % 合并种群
                population = [candidate; obj.record.archive];
            end
            
            % 获取种群规模
            [n, ~] = size(population);

            if n >= 3
                % 增加变异多样性 - 随机选择变异策略
                strategy = randi(2);  % 1: DE/rand/1, 2: DE/rand/2
                  if strategy == 1 || n < 5  % DE/rand/1 或 当种群太小时默认使用
                    % 选择多样性较好的三个个体
                    indices = obj.selectDiverseIndividuals(population, 3);
                    
                    % DE/rand/1变异
                    v = population(indices(1),:) + f * (population(indices(2),:) - population(indices(3),:));
                else  % DE/rand/2
                    % 选择5个不同的个体
                    indices = obj.selectDiverseIndividuals(population, 5);
                    
                    % DE/rand/2变异
                    v = population(indices(1),:) + ...
                        f * (population(indices(2),:) - population(indices(3),:)) + ...
                        f * (population(indices(4),:) - population(indices(5),:));
                end
            else
                % 如果可用个体太少，使用基础DE/rand/1加上随机扰动
                r1 = randi(n);
                available = setdiff(1:n, r1);
                if length(available) >= 2
                    r2 = available(randi(length(available)));
                    r3 = available(setdiff(1:length(available), r2));
                    r3 = r3(1);
                else
                    % 如果个体不足，使用随机向量
                    r2 = 1; r3 = 1;
                    if r1 == r2
                        r2 = min(n, r2 + 1);
                    end
                end
                
                v = population(r1,:) + f * (population(r2,:) - population(min(n, r3),:));
                
                % 添加额外随机扰动增加多样性，使用Record中的参数
                if rand < obj.record.random_disturbance_prob
                    random_dims = randperm(D, max(1, round(D * obj.record.random_disturbance_ratio)));
                    for dim = random_dims
                        v(dim) = v(dim) + (rand - 0.5) * obj.record.random_disturbance_scale;
                    end
                end
            end
            
            % 确保变异向量在[0,1]范围内
            v = max(0, min(1, v));
            
            % 二项式交叉
            j_rand = randi(D);  % 至少有一个维度发生变异
            offspring = candidate;
            
            for j = 1:D
                if rand <= cr || j == j_rand
                    offspring(j) = v(j);
                end
            end
            
            % 多样性增强
            % 计算种群中心
            center = mean(candidate, 1);
            % 计算当前子代到中心的欧式距离
            d1 = norm(offspring - center);
            % 对子代进行扰动，生成多样性增强候选解
            div_offspring = offspring;
            num_dims = max(1, round(D * obj.record.disturbance_ratio));
            dims = randperm(D, num_dims);
            for d = dims
                delta = (rand() - 0.5) * obj.record.disturbance_scale;
                div_offspring(d) = min(1, max(0, div_offspring(d) + delta));
            end
            % 计算扰动后子代到中心的欧式距离
            d2 = norm(div_offspring - center);
            
            % 根据多样性原则选择最终后代，使用Record中的参数
            diversity_threshold = obj.record.diversity_threshold;
            avg_distance = mean(vecnorm(candidate - center, 2, 2));  % 计算平均距离
            
            if avg_distance < diversity_threshold
                % 种群多样性不足，选择更远离中心的解
                if d2 > d1
                    offspring = div_offspring;
                end
            else
                % 种群多样性充足，选择更靠近中心的解
                if d2 < d1
                    offspring = div_offspring;
                end
            end
        end
        
        function indices = selectDiverseIndividuals(obj, population, num)
            [popSize, ~] = size(population);
            
            if popSize <= num
                % 如果种群大小不足需要选择的数量，返回所有可用索引
                indices = 1:popSize;
                % 如果还不够，随机重复选择
                if length(indices) < num
                    extra = randi(popSize, 1, num - length(indices));
                    indices = [indices, extra];
                end
                return;
            end
            
            % 计算种群中心
            center = mean(population, 1);
            
            % 计算每个个体到中心的距离
            distances = zeros(popSize, 1);
            for i = 1:popSize
                distances(i) = norm(population(i,:) - center);
            end
            
            % 初始化选择的索引
            indices = zeros(1, num);
            
            % 首先选择一个随机个体
            indices(1) = randi(popSize);
            
            % 然后选择剩余的个体，基于最大最小距离原则
            for i = 2:num
                max_min_dist = -inf;
                best_idx = 0;
                
                for j = 1:popSize
                    % 跳过已选个体
                    if ismember(j, indices(1:i-1))
                        continue;
                    end
                    
                    % 计算与已选个体的最小距离
                    min_dist = inf;
                    for k = 1:i-1
                        dist = norm(population(j,:) - population(indices(k),:));
                        min_dist = min(min_dist, dist);
                    end
                    
                    % 更新最大最小距离
                    if min_dist > max_min_dist
                        max_min_dist = min_dist;
                        best_idx = j;
                    end
                end
                
                indices(i) = best_idx;
            end
        end
        
        function updateMemorySHADE(obj, parent, offspring, improvement, population)
            % 更新成功历史记录，使用经典SHADE方法
            % parent: 父代个体
            % improvement: 改进量
            % offspring: 子代个体
            % population: 当前种群，用于额外信息
            
            % 添加到成功历史
            obj.record.success_cr = [obj.record.success_cr, obj.record.current_cr];
            obj.record.success_f = [obj.record.success_f, obj.record.current_f];
            obj.record.success_weight = [obj.record.success_weight, improvement];
            
            % 更新归档
            obj.record.archive = [obj.record.archive; parent];
            if size(obj.record.archive, 1) > obj.record.archive_size
                remove_indices = randperm(size(obj.record.archive, 1), size(obj.record.archive, 1) - obj.record.archive_size);
                obj.record.archive(remove_indices, :) = [];
            end
            
            % 定期更新记忆库
            if length(obj.record.success_cr) >= obj.record.memory_size * 0.5
                if ~isempty(obj.record.success_weight) && sum(obj.record.success_weight) > 0
                    % 计算权重 - 确保所有向量都是列向量或行向量，以保持一致性
                    sw = reshape(obj.record.success_weight, 1, []);  % 转为行向量
                    scr = reshape(obj.record.success_cr, 1, []);     % 转为行向量
                    sf = reshape(obj.record.success_f, 1, []);       % 转为行向量
                    
                    % 归一化权重
                    weights = max(0, sw) / sum(max(0, sw));
                    
                    % 更新CR记忆库 - 使用显式循环避免维度问题
                    weighted_cr = 0;
                    for i = 1:length(scr)
                        weighted_cr = weighted_cr + weights(i) * scr(i);
                    end
                    obj.record.memory_cr(obj.record.memory_pos) = weighted_cr;
                    
                    % 更新F记忆库 - 使用Lehmer均值，同样使用显式循环
                    weighted_f_squared = 0;
                    weighted_f = 0;
                    for i = 1:length(sf)
                        weighted_f_squared = weighted_f_squared + weights(i) * (sf(i)^2);
                        weighted_f = weighted_f + weights(i) * sf(i);
                    end
                    
                    if weighted_f > 0
                        obj.record.memory_f(obj.record.memory_pos) = weighted_f_squared / weighted_f;
                    else
                        obj.record.memory_f(obj.record.memory_pos) = 0.5;  % 默认值
                    end
                    
                    % 更新位置指针
                    obj.record.memory_pos = mod(obj.record.memory_pos, obj.record.memory_size) + 1;
                end
                
                % 清空成功历史
                obj.record.success_cr = [];
                obj.record.success_f = [];
                obj.record.success_weight = [];
            end
        end
        
        function updateArchiveSHADE(obj, population)
            % 实例方法，使用 obj 替代 Algorithm
            [N, ~] = size(population);

            % 随机选择一些个体添加到归档中
            num_to_add = min(N, obj.record.archive_size - size(obj.record.archive, 1));

            if num_to_add > 0
                indices = randperm(N, num_to_add);
                obj.record.archive = [obj.record.archive; population(indices, :)];
            end

            if size(obj.record.archive, 1) > obj.record.archive_size
                remove_indices = randperm(size(obj.record.archive, 1), size(obj.record.archive, 1) - obj.record.archive_size);
                obj.record.archive(remove_indices, :) = [];
            end
        end

        % ========== DE算法版本实现 ==========

        function offspring = DE_Version1_Rand1(obj, candidate, population_size, D)
            % 版本1: 经典DE/rand/1 - v = r1 + F * (r2 - r3)
            if population_size >= 3
                indices = randperm(population_size, 3);
                r1 = obj.record.archive(indices(1), :);
                r2 = obj.record.archive(indices(2), :);
                r3 = obj.record.archive(indices(3), :);

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = r1 + F * (r2 - r3);
                offspring = max(0, min(1, v));
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version2_Best1(obj, candidate, population_size, D)
            % 版本2: DE/best/1 - v = best + F * (r1 - r2)
            if population_size >= 3
                % 简化：随机选择一个作为"最优"个体
                best_idx = randi(population_size);
                best = obj.record.archive(best_idx, :);

                % 选择两个不同的个体
                other_indices = setdiff(1:population_size, best_idx);
                if length(other_indices) >= 2
                    selected = other_indices(randperm(length(other_indices), 2));
                    r1 = obj.record.archive(selected(1), :);
                    r2 = obj.record.archive(selected(2), :);
                else
                    % 不足时随机选择
                    indices = randperm(population_size, 2);
                    r1 = obj.record.archive(indices(1), :);
                    r2 = obj.record.archive(indices(2), :);
                end

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = best + F * (r1 - r2);
                offspring = max(0, min(1, v));
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version3_CurrentToBest1(obj, candidate, population_size, D)
            % 版本3: DE/current-to-best/1 - v = candidate + F * (best - candidate) + F * (r1 - r2)
            if population_size >= 3
                % 选择"最优"个体
                best_idx = randi(population_size);
                best = obj.record.archive(best_idx, :);

                % 选择两个随机个体
                other_indices = setdiff(1:population_size, best_idx);
                if length(other_indices) >= 2
                    selected = other_indices(randperm(length(other_indices), 2));
                    r1 = obj.record.archive(selected(1), :);
                    r2 = obj.record.archive(selected(2), :);
                else
                    indices = randperm(population_size, 2);
                    r1 = obj.record.archive(indices(1), :);
                    r2 = obj.record.archive(indices(2), :);
                end

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = candidate + F * (best - candidate) + F * (r1 - r2);
                offspring = max(0, min(1, v));
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version4_Rand2(obj, candidate, population_size, D)
            % 版本4: DE/rand/2 - v = r1 + F * (r2 - r3) + F * (r4 - r5)
            if population_size >= 5
                indices = randperm(population_size, 5);
                r1 = obj.record.archive(indices(1), :);
                r2 = obj.record.archive(indices(2), :);
                r3 = obj.record.archive(indices(3), :);
                r4 = obj.record.archive(indices(4), :);
                r5 = obj.record.archive(indices(5), :);

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = r1 + F * (r2 - r3) + F * (r4 - r5);
                offspring = max(0, min(1, v));
            elseif population_size >= 3
                % 退化为DE/rand/1
                offspring = obj.DE_Version1_Rand1(candidate, population_size, D);
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version5_Adaptive(obj, candidate, population_size, D)
            % 版本5: 自适应DE - 根据存档大小选择不同策略
            if population_size >= 10
                % 存档充足时使用DE/rand/2
                offspring = obj.DE_Version4_Rand2(candidate, population_size, D);
            elseif population_size >= 5
                % 中等存档时使用DE/current-to-best/1
                offspring = obj.DE_Version3_CurrentToBest1(candidate, population_size, D);
            elseif population_size >= 3
                % 存档较少时使用DE/rand/1
                offspring = obj.DE_Version1_Rand1(candidate, population_size, D);
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version6_Hybrid(obj, candidate, population_size, D)
            % 版本6: 混合DE - 随机选择不同策略
            if population_size >= 3
                strategy = randi(3); % 随机选择1-3种策略
                switch strategy
                    case 1
                        offspring = obj.DE_Version1_Rand1(candidate, population_size, D);
                    case 2
                        offspring = obj.DE_Version2_Best1(candidate, population_size, D);
                    case 3
                        offspring = obj.DE_Version3_CurrentToBest1(candidate, population_size, D);
                end
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Original_Transfer1(obj, candidate, population_size, D)
            % 原始DE/transfer/1策略
            if population_size >= 2
                indices = randperm(population_size, 2);
                r1 = obj.record.archive(indices(1), :);
                r2 = obj.record.archive(indices(2), :);

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = candidate + F * (r1 - r2);
                offspring = max(0, min(1, v));
            elseif population_size >= 1
                idx = randi(population_size);
                r1 = obj.record.archive(idx, :);

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = candidate + F * (r1 - candidate);
                offspring = max(0, min(1, v));
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Fallback_Polynomial(obj, candidate, D)
            % 存档不足时的后备多项式变异
            offspring = candidate;
            eta_m = 20;
            for j = 1:D
                if rand <= 1.0/D
                    u = rand;
                    if u <= 0.5
                        delta = (2*u)^(1/(eta_m+1)) - 1;
                    else
                        delta = 1 - (2*(1-u))^(1/(eta_m+1));
                    end
                    offspring(j) = candidate(j) + delta * obj.record.stage1_sigma_scale;
                    offspring(j) = max(0, min(1, offspring(j)));
                end
            end
        end
    end
end