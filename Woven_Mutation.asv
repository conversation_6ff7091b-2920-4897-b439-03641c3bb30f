classdef Woven_Mutation < handle    
    properties
        record;  % Woven_Record 对象，用于管理参数和存档
    end
    
    methods
        % 构造函数，初始化记录对象
        function obj = Woven_Mutation(record)
            obj.record = record;
        end
        
        function obj = initializeParameters(obj, memory_size, archive_rate, population_size)
            % 调用记录对象的初始化方法
            obj.record.initializeParameters(memory_size, archive_rate, population_size);
        end
        
        function offspring = mutate(obj, candidate, stage, fitness, Problem) 
            [~, D] = size(candidate);
            offspring = candidate;
            if stage == 1
                % 阶段1: 结合GA和DE/transfer/1的混合策略
                if rand < obj.record.stage1_normal_prob
                    % 使用GA高斯变异
                    k = randi(D);
                    distance_to_boundary = min(candidate(k), 1 - candidate(k));
                    sigma = min(0.5, distance_to_boundary * obj.record.stage1_sigma_scale);
                    temp = candidate(k) + randn * sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end
                else
                    % 多版本DE算法测试
                    % 临时变量控制使用哪个DE算法版本 (1-6)
                    DE_VERSION = 1; % 修改这个数字来测试不同版本

                    population_size = size(obj.record.archive, 1);

                    switch DE_VERSION
                        case 1
                            % 版本1: 经典DE/rand/1
                            offspring = obj.DE_Version1_Rand1(candidate, population_size, D);

                        case 2
                            % 版本2: DE/best/1
                            offspring = obj.DE_Version2_Best1(candidate, population_size, D);

                        case 3
                            % 版本3: DE/current-to-best/1
                            offspring = obj.DE_Version3_CurrentToBest1(candidate, population_size, D);

                        case 4
                            % 版本4: DE/rand/2
                            offspring = obj.DE_Version4_Rand2(candidate, population_size, D);

                        case 5
                            % 版本5: 自适应DE (根据存档大小选择策略)
                            offspring = obj.DE_Version5_Adaptive(candidate, population_size, D);

                        case 6
                            % 版本6: 混合DE (多策略随机选择)
                            offspring = obj.DE_Version6_Hybrid(candidate, population_size, D);

                        otherwise
                            % 默认使用原始DE/transfer/1
                            offspring = obj.DE_Original_Transfer1(candidate, population_size, D);
                    end
                end
            elseif stage == 2
                % 阶段2: 使用Record中的概率参数
                if rand < obj.record.stage2_normal_prob
                    % 正态变异
                    k = randi(D);
                    temp = candidate(k) + randn * obj.record.stage2_sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end                
                else
                    % DE/rand/1变异
                    population_size = size(obj.record.archive, 1);
                    if population_size >= 3
                        indices = randperm(population_size, 3);
                        r1 = obj.record.archive(indices(1), :);
                        r2 = obj.record.archive(indices(2), :);
                        r3 = obj.record.archive(indices(3), :);
                        
                        F = rand;
                        v = r1 + F * (r2 - r3);
                        
                        % 边界处理
                        v = max(0, min(1, v));
                        % 更新子代 - 不使用逻辑索引，而是直接赋值
                        offspring = v;
                    end
                end
            elseif stage == 3
                % 阶段3: 使用Record中的概率参数
                if rand < obj.record.stage3_normal_prob
                    % 正态变异
                    k = randi(D);
                    temp = candidate(k) + randn * obj.record.stage3_sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end                
                else
                    % 随机性更高的DE变异策略 - DE/rand/2
                    population_size = size(obj.record.archive, 1);
                    if population_size >= 5  % DE/rand/2需要至少5个不同的个体
                        % 随机选择5个不同的个体
                        indices = randperm(population_size, 5);
                        r1 = obj.record.archive(indices(1), :);
                        r2 = obj.record.archive(indices(2), :);
                        r3 = obj.record.archive(indices(3), :);
                        r4 = obj.record.archive(indices(4), :);
                        r5 = obj.record.archive(indices(5), :);
                        
                        % 控制参数F，使用Record中的范围参数
                        F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                        
                        % DE/rand/2变异操作
                        v = r1 + F * (r2 - r3) + F * (r4 - r5);
                
                        % 边界处理
                        v = max(0, min(1, v));
                        % 直接赋值
                        offspring = v;
                    elseif population_size >= 3  % 如果个体不足5个，退化为DE/rand/1
                        % 随机选择3个不同的个体
                        indices = randperm(population_size, 3);
                        r1 = obj.record.archive(indices(1), :);
                        r2 = obj.record.archive(indices(2), :);
                        r3 = obj.record.archive(indices(3), :);
            
                        % 控制参数F，使用Record中的范围参数
                        F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
            
                        % DE/rand/1变异操作
                        v = r1 + F * (r2 - r3);
                
                        % 边界处理
                        v = max(0, min(1, v));
                        % 直接赋值
                        offspring = v;
                    elseif population_size >= 1  % 如果个体非常少，使用当前个体加随机扰动
                        % 随机选择一个存档个体
                        idx = randi(population_size);
                        r1 = obj.record.archive(idx, :);
            
                        % 大幅度随机扰动，使用Record中的参数
                        v = r1;
                        num_dims = max(1, round(D * obj.record.large_disturbance_ratio));
                        dims = randperm(D, num_dims);
                        for dim = dims
                            v(dim) = v(dim) + (rand - 0.5) * obj.record.large_disturbance_scale;
                        end
            
                        % 边界处理
                        v = max(0, min(1, v));
                        % 直接赋值
                        offspring = v;
                    end
                end
            elseif stage == 4
                if rand < obj.record.stage1_normal_prob
                    % 使用正态变异
                    k = randi(D);
                    distance_to_boundary = min(candidate(k), 1 - candidate(k));
                    sigma = min(0.5, distance_to_boundary * obj.record.stage1_sigma_scale);
                    temp = candidate(k) + randn * sigma;
                    if temp >= 0 && temp <= 1
                        offspring(k) = temp;
                    end
                else
                    % 使用SHADE生成后代
                    offspring = obj.generateOffspringSHADE(candidate, fitness, Problem);
                end  
            end
        end
        
        
        function indices = selectDiverseIndividuals(obj, population, num)
            [popSize, ~] = size(population);
            
            if popSize <= num
                % 如果种群大小不足需要选择的数量，返回所有可用索引
                indices = 1:popSize;
                % 如果还不够，随机重复选择
                if length(indices) < num
                    extra = randi(popSize, 1, num - length(indices));
                    indices = [indices, extra];
                end
                return;
            end
            
            % 计算种群中心
            center = mean(population, 1);
            
            % 计算每个个体到中心的距离
            distances = zeros(popSize, 1);
            for i = 1:popSize
                distances(i) = norm(population(i,:) - center);
            end
            
            % 初始化选择的索引
            indices = zeros(1, num);
            
            % 首先选择一个随机个体
            indices(1) = randi(popSize);
            
            % 然后选择剩余的个体，基于最大最小距离原则
            for i = 2:num
                max_min_dist = -inf;
                best_idx = 0;
                
                for j = 1:popSize
                    % 跳过已选个体
                    if ismember(j, indices(1:i-1))
                        continue;
                    end
                    
                    % 计算与已选个体的最小距离
                    min_dist = inf;
                    for k = 1:i-1
                        dist = norm(population(j,:) - population(indices(k),:));
                        min_dist = min(min_dist, dist);
                    end
                    
                    % 更新最大最小距离
                    if min_dist > max_min_dist
                        max_min_dist = min_dist;
                        best_idx = j;
                    end
                end
                
                indices(i) = best_idx;
            end
        end
        

       

        % ========== DE算法版本实现 ==========

        function offspring = DE_Version1_Rand1(obj, candidate, population_size, D)
            % 版本1: 经典DE/rand/1 - v = r1 + F * (r2 - r3)
            if population_size >= 3
                indices = randperm(population_size, 3);
                r1 = obj.record.archive(indices(1), :);
                r2 = obj.record.archive(indices(2), :);
                r3 = obj.record.archive(indices(3), :);

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = r1 + F * (r2 - r3);
                offspring = max(0, min(1, v));
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version2_Best1(obj, candidate, population_size, D)
            % 版本2: DE/best/1 - v = best + F * (r1 - r2)
            if population_size >= 3
                % 简化：随机选择一个作为"最优"个体
                best_idx = randi(population_size);
                best = obj.record.archive(best_idx, :);

                % 选择两个不同的个体
                other_indices = setdiff(1:population_size, best_idx);
                if length(other_indices) >= 2
                    selected = other_indices(randperm(length(other_indices), 2));
                    r1 = obj.record.archive(selected(1), :);
                    r2 = obj.record.archive(selected(2), :);
                else
                    % 不足时随机选择
                    indices = randperm(population_size, 2);
                    r1 = obj.record.archive(indices(1), :);
                    r2 = obj.record.archive(indices(2), :);
                end

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = best + F * (r1 - r2);
                offspring = max(0, min(1, v));
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version3_CurrentToBest1(obj, candidate, population_size, D)
            % 版本3: DE/current-to-best/1 - v = candidate + F * (best - candidate) + F * (r1 - r2)
            if population_size >= 3
                % 选择"最优"个体
                best_idx = randi(population_size);
                best = obj.record.archive(best_idx, :);

                % 选择两个随机个体
                other_indices = setdiff(1:population_size, best_idx);
                if length(other_indices) >= 2
                    selected = other_indices(randperm(length(other_indices), 2));
                    r1 = obj.record.archive(selected(1), :);
                    r2 = obj.record.archive(selected(2), :);
                else
                    indices = randperm(population_size, 2);
                    r1 = obj.record.archive(indices(1), :);
                    r2 = obj.record.archive(indices(2), :);
                end

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = candidate + F * (best - candidate) + F * (r1 - r2);
                offspring = max(0, min(1, v));
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version4_Rand2(obj, candidate, population_size, D)
            % 版本4: DE/rand/2 - v = r1 + F * (r2 - r3) + F * (r4 - r5)
            if population_size >= 5
                indices = randperm(population_size, 5);
                r1 = obj.record.archive(indices(1), :);
                r2 = obj.record.archive(indices(2), :);
                r3 = obj.record.archive(indices(3), :);
                r4 = obj.record.archive(indices(4), :);
                r5 = obj.record.archive(indices(5), :);

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = r1 + F * (r2 - r3) + F * (r4 - r5);
                offspring = max(0, min(1, v));
            elseif population_size >= 3
                % 退化为DE/rand/1
                offspring = obj.DE_Version1_Rand1(candidate, population_size, D);
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version5_Adaptive(obj, candidate, population_size, D)
            % 版本5: 自适应DE - 根据存档大小选择不同策略
            if population_size >= 10
                % 存档充足时使用DE/rand/2
                offspring = obj.DE_Version4_Rand2(candidate, population_size, D);
            elseif population_size >= 5
                % 中等存档时使用DE/current-to-best/1
                offspring = obj.DE_Version3_CurrentToBest1(candidate, population_size, D);
            elseif population_size >= 3
                % 存档较少时使用DE/rand/1
                offspring = obj.DE_Version1_Rand1(candidate, population_size, D);
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Version6_Hybrid(obj, candidate, population_size, D)
            % 版本6: 混合DE - 随机选择不同策略
            if population_size >= 3
                strategy = randi(3); % 随机选择1-3种策略
                switch strategy
                    case 1
                        offspring = obj.DE_Version1_Rand1(candidate, population_size, D);
                    case 2
                        offspring = obj.DE_Version2_Best1(candidate, population_size, D);
                    case 3
                        offspring = obj.DE_Version3_CurrentToBest1(candidate, population_size, D);
                end
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Original_Transfer1(obj, candidate, population_size, D)
            % 原始DE/transfer/1策略
            if population_size >= 2
                indices = randperm(population_size, 2);
                r1 = obj.record.archive(indices(1), :);
                r2 = obj.record.archive(indices(2), :);

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = candidate + F * (r1 - r2);
                offspring = max(0, min(1, v));
            elseif population_size >= 1
                idx = randi(population_size);
                r1 = obj.record.archive(idx, :);

                F = obj.record.de_f_min + (obj.record.de_f_max - obj.record.de_f_min) * rand;
                v = candidate + F * (r1 - candidate);
                offspring = max(0, min(1, v));
            else
                offspring = obj.DE_Fallback_Polynomial(candidate, D);
            end
        end

        function offspring = DE_Fallback_Polynomial(obj, candidate, D)
            % 存档不足时的后备多项式变异
            offspring = candidate;
            eta_m = 20;
            for j = 1:D
                if rand <= 1.0/D
                    u = rand;
                    if u <= 0.5
                        delta = (2*u)^(1/(eta_m+1)) - 1;
                    else
                        delta = 1 - (2*(1-u))^(1/(eta_m+1));
                    end
                    offspring(j) = candidate(j) + delta * obj.record.stage1_sigma_scale;
                    offspring(j) = max(0, min(1, offspring(j)));
                end
            end
        end
    end
end