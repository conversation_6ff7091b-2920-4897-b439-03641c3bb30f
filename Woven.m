classdef Woven < ALGORITHM
    % <multi/many> <real/integer/label/binary/permutation> <constrained/large/expensivie/multimodal/sparse/dynamic/multitask/bilevel/robust>
    % Woven - 约束多目标进化策略
    % 三阶段约束多目标进化策略
    % memory_size --- 5 --- 记忆库大小
    % archive_rate --- 1.0 --- 存档率
    
    properties
        stage = 1;          % Current stage (当前阶段)
        obj_flags = [];     % Objective flags (目标标志)
        mutation;           % Mutation operator
        selection;          % Selection operator
        stageControl;      % Stage control
        record;            % Record instance
    end
    methods
        function main(Algorithm,Problem)
            Algorithm.record = Woven_Record.getInstance(Algorithm.ParameterSet('memory_size', 5),Algorithm.ParameterSet('archive_rate', 1.0), Problem.N);
            
            Algorithm.mutation = Woven_Mutation(Algorithm.record);
            Algorithm.selection = Woven_Selection(Algorithm.record);
            Algorithm.stageControl = Woven_StageControl(Algorithm.record);
            
            % 初始化种群
            Population = Problem.Initialization();
            PopDec = Population.decs;
            PopObj = Population.objs;
            PopCon = Population.cons;
            disp('当前运行版本为第三阶段收敛性提升版12111')
            while Algorithm.NotTerminated(Population)
                switch Algorithm.stage
                    case 1
                        [PopDec, PopObj, PopCon] = Algorithm.firstStage(Problem, PopDec, PopObj, PopCon);
                        Algorithm.obj_flags = Woven_Utility.calculateStage1NonDominatedSet(PopObj);
                        if Algorithm.stageControl.checkStage1Transition(Problem, PopObj, PopCon, Algorithm.obj_flags)
                            fprintf('=== 进入第二阶段 ===\n');
                            fprintf('当前评估次数: %d / %d\n', Problem.FE, Problem.maxFE);
                            Algorithm.record.stage2_start_fe = Problem.FE;
                            Algorithm.stage = 2;
                        end
                    case 2
                        [PopDec, PopObj, PopCon] = Algorithm.secondStage(Problem, PopDec, PopObj, PopCon);
                        if Algorithm.stageControl.checkStage2Transition(Problem, PopDec, PopObj, PopCon)
                            fprintf('=== 进入第三阶段 ===\n');
                            fprintf('当前评估次数: %d / %d\n', Problem.FE, Problem.maxFE);
                            Algorithm.record.stage3_start_fe = Problem.FE;
                            Algorithm.stage = 3;
                        end
                    case 3
                        [PopDec, PopObj, PopCon] = Algorithm.thirdStage(Problem, PopDec, PopObj, PopCon); 
                end
                Population = SOLUTION(PopDec, PopObj, PopCon);
            end
        end
        
        function [PopDec, PopObj, PopCon] = firstStage(Algorithm, Problem, PopDec, PopObj, PopCon)
            N = size(PopDec,1);
            for i = 1:N
                old_dec = PopDec(i,:);
                old_obj = PopObj(i,:);
                new_dec = Algorithm.mutation.mutate(old_dec, 1, old_obj, Problem);
                offspring = Problem.Evaluation(new_dec);
                new_obj = offspring.objs;
                new_con = offspring.cons; 
                selected = Algorithm.selection.Stage1Selection(old_dec, old_obj, new_dec, new_obj, new_con, i, PopDec, PopObj);
                if selected
                    obj_range = max(PopObj) - min(PopObj);
                    normalized_diff = (old_obj - new_obj) ./ (obj_range + 1e-10);
                    improvement = norm(normalized_diff);
                    Algorithm.mutation.updateMemorySHADE(old_dec, new_dec, improvement, PopDec);
                    PopDec(i,:) = new_dec;
                    PopObj(i,:) = new_obj;
                    PopCon(i,:) = new_con;
                end
            end
            Algorithm.mutation.updateArchiveSHADE(PopDec);
        end
        function [PopDec, PopObj, PopCon] = secondStage(Algorithm, Problem, PopDec, PopObj, PopCon)
            % 计算ε约束值
            stage2_elapsed_fe = Problem.FE - Algorithm.record.stage2_start_fe;
            stage2_max_fe = Problem.maxFE * Algorithm.record.stage2_max_fe - Algorithm.record.stage2_start_fe;
            epsilon_values = Woven_Utility.calculateEpsilonValues(PopCon, stage2_elapsed_fe, stage2_max_fe, Algorithm.record.constraint_beta);
            
            N = size(PopDec, 1);
            
            % 计算考虑约束的第一前沿解
            first_layer_flags = Woven_Utility.calculateStage23NonDominatedSet(PopObj, PopCon, Problem);
            
            for i = 1:N
                old_dec = PopDec(i,:);
                old_obj = PopObj(i,:);
                old_con = PopCon(i,:);
                
                new_dec = Algorithm.mutation.mutate(old_dec, 2);
                
                offspring = Problem.Evaluation(new_dec);
                new_obj = offspring.objs;
                new_con = offspring.cons;
                
                selected = Algorithm.selection.Stage2Selection(old_dec, old_obj, old_con, new_dec, new_obj, new_con, i, PopDec, PopObj, PopCon, epsilon_values, first_layer_flags);
                
                if selected
                    PopDec(i,:) = new_dec;
                    PopObj(i,:) = new_obj;
                    PopCon(i,:) = new_con;
                end
            end
        end

        function [PopDec, PopObj, PopCon] = thirdStage(Algorithm, Problem, PopDec, PopObj, PopCon)
            % 第三阶段复用第二阶段的逻辑，但使用严格的约束处理（epsilon=0）
            epsilon_values = zeros(1, size(PopCon, 2)); % 第三阶段使用严格约束（epsilon=0）
            
            N = size(PopDec, 1);
            
            % 计算考虑约束的第一前沿解
            first_layer_flags = Woven_Utility.calculateStage23NonDominatedSet(PopObj, PopCon, Problem);
            
            for i = 1:N
                old_dec = PopDec(i,:);
                old_obj = PopObj(i,:);
                old_con = PopCon(i,:);
                
                new_dec = Algorithm.mutation.mutate(old_dec, 3, old_obj, Problem);
                
                offspring = Problem.Evaluation(new_dec);
                new_obj = offspring.objs;
                new_con = offspring.cons;
                selected = Algorithm.selection.Stage3Selection(old_dec, old_obj, old_con, new_dec, new_obj, new_con, i, PopDec, PopObj, PopCon, first_layer_flags, Problem);
                
                if selected
                    PopDec(i,:) = new_dec;
                    PopObj(i,:) = new_obj;
                    PopCon(i,:) = new_con;
                end
            end
        end
    end
end